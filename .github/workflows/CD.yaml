name: 'CD'

on:
  push:
    branches:
      - main

env:
  PYTHON_VERSION: '3.11'
  PROJECT_NAME: 'ai-agent-utils'

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          cache: 'pip'
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Dependencies
        run: |
          pip install -r requirements-dev.txt

      - name: Build the Package
        run: |
          pip install build
          python -m build --wheel

      # - name: Run Tests
      #   run: pytest tests/

      - name: Upload Build Artifact
        uses: actions/upload-artifact@v4
        with:
          name: dist
          path: dist/*

  publish-package:
    name: Publish Package
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Build Artifact
        uses: actions/download-artifact@v4
        with:
          name: dist
          path: dist

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          cache: 'pip'
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Build Tools
        run: |
          pip install --upgrade pip
          pip install setuptools wheel

      - name: Extract Version
        id: extract_version
        run: |
          VERSION=$(ls dist/*.whl | sed -E 's/.*-([0-9]+\.[0-9]+\.[0-9]+).*\.whl/\1/')
          echo "Version is $VERSION"
          echo "version=$VERSION" >> $GITHUB_OUTPUT

      - name: Publish to Nexus
        env:
          NEXUS_AUTH_USER: ${{ secrets.NEXUS_AUTH_USER }}
          NEXUS_AUTH_TOKEN: ${{ secrets.NEXUS_AUTH_TOKEN }}
        run: |
          pip install twine
          python -m twine upload --repository-url https://nexus.lifetime.life/repository/lttpip-publish/ dist/* -u $NEXUS_AUTH_USER -p $NEXUS_AUTH_TOKEN

  # tag-release:
    # needs: [publish-package]
    # uses: Life-Time-Inc/workflows-library/.github/workflows/github-tag-release.yml@github-tag-release-v1
    # with:
    #   TAG: ${{ needs.publish-package.steps.extract_version.outputs.version }}
    #   PRERELEASE: ${{ github.event.ref != 'refs/heads/main' }}
