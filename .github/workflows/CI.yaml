name: 'CI'

on:
  pull_request:
    branches:
      - main

env:
  PYTHON_VERSION: '3.11'

jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Fetch main branch for version comparison
        run: |
          # This ensures we can compare files to what's on main
          git fetch origin main

      - name: Check version bump
        run: |
          # Extract version from main branch (pyproject.toml)
          MAIN_VERSION=$(git show origin/main:pyproject.toml | grep '^version =' | sed -E 's/version = "(.*)"/\1/')
          echo "Main branch version: $MAIN_VERSION"

          # Extract version from current PR branch (the checked-out code)
          PR_VERSION=$(grep '^version =' pyproject.toml | sed -E 's/version = "(.*)"/\1/')
          echo "PR branch version: $PR_VERSION"

          # If they match, fail the job
          if [ "$PR_VERSION" = "$MAIN_VERSION" ]; then
            echo "ERROR: Version has not been bumped. Please update the version in pyproject.toml."
            exit 1
          fi

      - name: Set up Python ${{ env.PYTHON_VERSION }}
        uses: actions/setup-python@v5
        with:
          cache: 'pip'
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Install Dependencies
        run: |
          pip install -r requirements.txt
          pip install pytest coverage

      - name: Run Tests with Coverage
        run: coverage run -m pytest tests/

      - name: Generate Coverage HTML Report
        run: coverage html --omit=tests/*

      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: htmlcov

      - name: Check Coverage
        run: coverage report --omit=tests/* --fail-under=80
