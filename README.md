
# AI Agent Utils

A shared library for AI chatbot agents.

## Installation

To install ai-agent-utils (from PyPI if published):

```bash
uv add ai-agent-utils
```

## Local Development

This project uses [uv](https://docs.astral.sh/uv/) for dependency management and packaging.

### 1. Install uv
If you don't have uv installed:
```bash
curl -LsSf https://astral.sh/uv/install.sh | sh
```

### 2. Install Dependencies
Install the project and its dependencies in development mode:
```bash
uv sync --extra dev
```

### 3. Run Tests
```bash
uv run pytest tests/
```

## Build for Release and Publish to Nexus

Using uv:
```bash
uv build
uv publish --repository-url https://nexus.lifetime.life/repository/lttpip-publish/ --username enumber --password 'password'
```
