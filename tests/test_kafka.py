import pytest
import os
import json
import platform
import pytz
from datetime import datetime
from unittest.mock import patch, MagicMock, call
from ai_agent_utils.services.kafka import KafkaClient

class TestKafkaClient:

    @pytest.fixture(autouse=True)
    def setup_and_teardown(self):
        """Reset class attributes before and after each test"""
        # Save original values
        self.orig_conf = KafkaClient.conf
        self.orig_producer = KafkaClient.producer
        
        yield
        
        # Restore original values
        KafkaClient.conf = self.orig_conf
        KafkaClient.producer = self.orig_producer

    @patch('ai_agent_utils.services.kafka.Producer')
    @patch.dict(os.environ, {
        "KAFKA_BROKERS": "test-broker:9092",
        "KAFKA_CLIENT_ID": "test-client",
        "KAFKA_SASL_USERNAME": "test-user",
        "KAFKA_SASL_PASSWORD": "test-pass"
    })
    def test_kafka_conf_with_env_vars(self, mock_producer):
        """Test kafka_conf using environment variables"""
        KafkaClient.kafka_conf()
        
        assert KafkaClient.conf['bootstrap.servers'] == "test-broker:9092"
        assert KafkaClient.conf['client.id'] == "test-client"
        assert KafkaClient.conf['sasl.username'] == "test-user"
        assert KafkaClient.conf['sasl.password'] == "test-pass"
        assert KafkaClient.conf['sasl.mechanism'] == 'PLAIN'
        assert KafkaClient.conf['security.protocol'] == 'SASL_SSL'
        
        mock_producer.assert_called_once_with(KafkaClient.conf)

    @patch('ai_agent_utils.services.kafka.Producer')
    def test_kafka_conf_with_parameters(self, mock_producer):
        """Test kafka_conf using passed parameters"""
        KafkaClient.kafka_conf(
            client_id="param-client",
            brokers="param-broker:9092",
            username="param-user",
            password="param-pass"
        )
        
        assert KafkaClient.conf['bootstrap.servers'] == "param-broker:9092"
        assert KafkaClient.conf['client.id'] == "param-client"
        assert KafkaClient.conf['sasl.username'] == "param-user"
        assert KafkaClient.conf['sasl.password'] == "param-pass"
        
        mock_producer.assert_called_once_with(KafkaClient.conf)

    @patch('ai_agent_utils.services.kafka.Producer')
    def test_init(self, mock_producer):
        """Test the __init__ method"""
        with patch.object(KafkaClient, 'kafka_conf') as mock_kafka_conf:
            KafkaClient(
                client_id="init-client",
                brokers="init-broker:9092",
                username="init-user",
                password="init-pass"
            )
            
            mock_kafka_conf.assert_called_once_with(
                "init-client", "init-broker:9092", "init-user", "init-pass"
            )

    def test_delivery_report_success(self):
        """Test delivery_report with successful delivery"""
        KafkaClient.logger = MagicMock()
        msg = MagicMock()
        
        KafkaClient.delivery_report(None, msg)
        
        # No error should be logged on success
        KafkaClient.logger.error.assert_not_called()

    def test_delivery_report_failure(self):
        """Test delivery_report with failed delivery"""
        KafkaClient.logger = MagicMock()
        msg = MagicMock()
        msg.key.return_value = "test-key"
        msg.topic.return_value = "test-topic"
        err = Exception("Delivery failed")
        
        KafkaClient.delivery_report(err, msg)
        
        KafkaClient.logger.error.assert_called_once_with(
            "Delivery failed for User record",
            {"key": "test-key", "topic": "test-topic", "error": str(err)}
        )

    @patch('ai_agent_utils.services.kafka.datetime')
    @patch.dict(os.environ, {"AI_LAIC_CHAT_PERFORMANCE_TOPIC": "test-topic"})
    def test_handle_timing_output(self, mock_datetime):
        """Test _handle_timing_output method"""
        # Setup
        KafkaClient.producer = MagicMock()
        mock_func = MagicMock(__name__="test_function")
        
        # Mock datetime.now to return a fixed value
        fixed_datetime = datetime(2023, 1, 1, 12, 0, 0, tzinfo=pytz.UTC)
        mock_datetime.now.return_value = fixed_datetime
        
        # Call the method
        KafkaClient._handle_timing_output(
            func=mock_func,
            service_name="test-service",
            model="test-model",
            metadata={"test": "data"},
            execution_time=0.12345,
            debug=True
        )
        
        # Expected output
        expected_output = {
            "Service": "test-service",
            "Function": "test_function",
            "OS_Version": platform.system(),
            "Model": "test-model",
            "Execution_Time": "0.123450000000",
            "Metadata": "{'test': 'data'}",
            "PublishTimestamp": "2023-01-01T12:00:00.000Z"
        }
        
        # Verify produce was called with correct arguments
        KafkaClient.producer.produce.assert_called_once()
        args, kwargs = KafkaClient.producer.produce.call_args
        
        assert kwargs["topic"] == "test-topic"
        assert kwargs["key"] == json.dumps({"service": "test-service"})
        assert json.loads(kwargs["value"]) == expected_output
        assert kwargs["on_delivery"] == KafkaClient.delivery_report
        
        # Verify flush was called
        KafkaClient.producer.flush.assert_called_once()