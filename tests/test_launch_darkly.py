import os
import pytest
from unittest.mock import patch, <PERSON><PERSON><PERSON>, <PERSON><PERSON>
from ldclient.client import <PERSON><PERSON><PERSON>
from ldclient.config import Config
from ldclient.context import Context
from cachetools import TTLCache

from ai_agent_utils.services.launch_darkly import LaunchDarklyClient


class TestLaunchDarklyClient:

    def setup_method(self):
        """Reset the client and cache before each test"""
        LaunchDarklyClient.client = None
        LaunchDarklyClient.cache = TTLCache(maxsize=1, ttl=60)

    def teardown_method(self):
        """Clean up after each test"""
        if LaunchDarklyClient.client:
            LaunchDarklyClient.close()

    @patch('ai_agent_utils.services.launch_darkly.LDClient')
    @patch('ai_agent_utils.services.launch_darkly.Config')
    def test_connect_success(self, mock_config, mock_ld_client):
        """Test successful connection with explicit SDK key"""
        # Arrange
        mock_config_instance = MagicMock()
        mock_config.return_value = mock_config_instance
        mock_client_instance = MagicMock()
        mock_ld_client.return_value = mock_client_instance

        # Act
        LaunchDarklyClient.connect(sdk_key="test-sdk-key")

        # Assert
        mock_config.assert_called_once_with(sdk_key="test-sdk-key")
        mock_ld_client.assert_called_once_with(config=mock_config_instance)
        assert LaunchDarklyClient.client is mock_client_instance

    @patch('ai_agent_utils.services.launch_darkly.LDClient')
    @patch('ai_agent_utils.services.launch_darkly.Config')
    def test_connect_with_env_var(self, mock_config, mock_ld_client):
        """Test connection using environment variable"""
        # Arrange
        mock_config_instance = MagicMock()
        mock_config.return_value = mock_config_instance
        mock_client_instance = MagicMock()
        mock_ld_client.return_value = mock_client_instance

        # Mock environment variable
        with patch.dict(os.environ, {"LD_SDK_KEY": "env-sdk-key"}):
            # Act
            LaunchDarklyClient.connect()

            # Assert
            mock_config.assert_called_once_with(sdk_key="env-sdk-key")
            mock_ld_client.assert_called_once_with(config=mock_config_instance)
            assert LaunchDarklyClient.client is mock_client_instance

    def test_connect_no_sdk_key(self):
        """Test error handling when no SDK key is provided"""
        # Arrange
        with patch.dict(os.environ, {}, clear=True):  # Clear environment variables

            # Act & Assert
            with pytest.raises(Exception) as excinfo:
                LaunchDarklyClient.connect()

            assert str(excinfo.value) == "SDK Key is required"

    @patch('ai_agent_utils.services.launch_darkly.Config')
    @patch('ai_agent_utils.services.launch_darkly.LDClient')
    def test_connect_exception(self, mock_ld_client, mock_config):
        """Test error handling when connection fails"""
        # Arrange
        mock_config_instance = MagicMock()
        mock_config.return_value = mock_config_instance
        mock_ld_client.side_effect = Exception("Connection error")

        # Mock the logger
        original_logger = LaunchDarklyClient.logger
        mock_logger = Mock()
        LaunchDarklyClient.logger = mock_logger

        try:
            # Act & Assert
            with pytest.raises(Exception) as excinfo:
                LaunchDarklyClient.connect(sdk_key="test-sdk-key")

            assert str(excinfo.value) == "Connection error"

            # Verify logger was called with error
            mock_logger.error.assert_called_once_with(
                "Failed to connect to LaunchDarkly",
                {"exception": "Connection error"}
            )
        finally:
            # Restore original logger
            LaunchDarklyClient.logger = original_logger

    @patch('ai_agent_utils.services.launch_darkly.LDClient')
    @patch('ai_agent_utils.services.launch_darkly.Config')
    def test_close(self, mock_config, mock_ld_client):
        """Test successful client closure"""
        # Arrange
        mock_config_instance = MagicMock()
        mock_config.return_value = mock_config_instance
        mock_client_instance = MagicMock()
        mock_ld_client.return_value = mock_client_instance

        LaunchDarklyClient.connect(sdk_key="test-sdk-key")

        # Act
        LaunchDarklyClient.close()

        # Assert
        mock_client_instance.close.assert_called_once()

    def test_close_no_client(self):
        """Test closing when no client exists"""
        # Arrange
        LaunchDarklyClient.client = None

        # Act - Should not raise exception
        LaunchDarklyClient.close()

    @patch('ai_agent_utils.services.launch_darkly.Context')
    def test_get_user_context_mixed_values(self, mock_context):
        """Test context creation with mix of provided and default values"""
        # Arrange
        scratch_pad = {
            "entityId": "test-entity",
            "memberId": "test-member",
        }
        mock_builder = MagicMock()
        mock_context.builder.return_value = mock_builder
        mock_built_context = MagicMock()
        mock_builder.build.return_value = mock_built_context

        # Act
        result = LaunchDarklyClient.get_user_context(scratch_pad)

        # Assert
        mock_context.builder.assert_called_once_with("test-entity")
        mock_builder.set.assert_any_call("partyId", "")
        mock_builder.set.assert_any_call("memberId", "test-member")
        mock_builder.set.assert_any_call("appVersion", "")
        mock_builder.set.assert_any_call("platform", "")
        mock_builder.build.assert_called_once()
        assert result == mock_built_context
    
        @patch.object(LaunchDarklyClient, 'client')
        def test_variation_boolean(self, mock_client):
            """Test boolean flag variation evaluation"""
            # Arrange
            mock_context = MagicMock()
            mock_client.variation.return_value = True
            
            # Act
            result = LaunchDarklyClient.client.variation("test-flag", mock_context, False)
            
            # Assert
            assert result is True
            mock_client.variation.assert_called_once_with("test-flag", mock_context, False)

        @patch.object(LaunchDarklyClient, 'client')
        def test_variation_string(self, mock_client):
            """Test string flag variation evaluation"""
            # Arrange
            mock_context = MagicMock()
            mock_client.variation.return_value = "test-value"
            
            # Act
            result = LaunchDarklyClient.client.variation("test-flag", mock_context, "default")
            
            # Assert
            assert result == "test-value"
            mock_client.variation.assert_called_once_with("test-flag", mock_context, "default")

        @patch.object(LaunchDarklyClient, 'client')
        def test_variation_number(self, mock_client):
            """Test number flag variation evaluation"""
            # Arrange
            mock_context = MagicMock()
            mock_client.variation.return_value = 42
            
            # Act
            result = LaunchDarklyClient.client.variation("test-flag", mock_context, 0)
            
            # Assert
            assert result == 42
            mock_client.variation.assert_called_once_with("test-flag", mock_context, 0)

        @patch.object(LaunchDarklyClient, 'client')
        def test_variation_json(self, mock_client):
            """Test JSON flag variation evaluation"""
            # Arrange
            mock_context = MagicMock()
            json_value = {"key": "value", "nested": {"inner": True}}
            mock_client.variation.return_value = json_value
            
            # Act
            result = LaunchDarklyClient.client.variation("test-flag", mock_context, {})
            
            # Assert
            assert result == json_value
            mock_client.variation.assert_called_once_with("test-flag", mock_context, {})

        @patch.object(LaunchDarklyClient, 'client')
        def test_variation_default_value(self, mock_client):
            """Test default value returned when flag evaluation fails"""
            # Arrange
            mock_context = MagicMock()
            mock_client.variation.return_value = None
            
            # Act
            result = LaunchDarklyClient.client.variation("test-flag", mock_context, "fallback")
            
            # Assert
            assert result is None
            mock_client.variation.assert_called_once_with("test-flag", mock_context, "fallback")
    
    @patch('ai_agent_utils.services.launch_darkly.Context')
    def test_get_user_context_with_values(self, mock_context):
        """Test context creation with provided values"""
        # Arrange
        scratch_pad = {
            "entityId": "test-entity",
            "partyId": "test-party",
            "memberId": "test-member",
            "appVersion": "1.0.0",
            "platform": "iOS"
        }
        mock_builder = MagicMock()
        mock_context.builder.return_value = mock_builder
        mock_built_context = MagicMock()
        mock_builder.build.return_value = mock_built_context

        # Act
        result = LaunchDarklyClient.get_user_context(scratch_pad)

        # Assert
        mock_context.builder.assert_called_once_with("test-entity")
        mock_builder.set.assert_any_call("partyId", "test-party")
        mock_builder.set.assert_any_call("memberId", "test-member")
        mock_builder.set.assert_any_call("appVersion", "1.0.0")
        mock_builder.set.assert_any_call("platform", "iOS")
        mock_builder.build.assert_called_once()
        assert result == mock_built_context
