import pytest
import pytest_asyncio
import json
from unittest.mock import patch, Async<PERSON>ock
import redis.asyncio as redis

from ai_agent_utils.services.redis import Redis
import pytest
import pytest_asyncio
import math
from unittest.mock import patch, AsyncMock, MagicMock

@pytest_asyncio.fixture
async def setup_redis():
    """Reset Redis client before each test and clean up afterward"""
    Redis.client = None
    yield
    await Redis.close()

class TestRedis:
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.redis.Redis')
    @patch('ai_agent_utils.services.redis.logger')
    async def test_connect_error(self, mock_logger, mock_redis, setup_redis):
        """Test handling of connection errors"""
        
        mock_redis.side_effect = redis.RedisError("Connection failed")
        
        with pytest.raises(redis.RedisError):
            await Redis.connect(host="test-host", password="test-password")
        
        mock_logger.error.assert_called_once_with(
            "Failed to connect to <PERSON><PERSON>", 
            {"error": "Connection failed"}
        )
    
    @pytest.mark.asyncio
    async def test_close_with_client(self, setup_redis):
        """Test successful closure of Redis connection"""
        Redis.client = AsyncMock()
        await Redis.close()

        Redis.client.aclose.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_no_client(self, setup_redis):
        """Test that close doesn't error when no client exists"""
        #the test passes if no exception is raised
        Redis.client = None
        await Redis.close()
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.logger')
    async def test_set_success(self, mock_logger, setup_redis):
        """Test successful setting of a key-value pair"""
        
        Redis.client = AsyncMock()
        Redis.client.set = AsyncMock()
        
        await Redis.set("test-key", "test-value")
        
        Redis.client.set.assert_called_once_with("test-key", "test-value", ex=3600)
        mock_logger.error.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.logger')
    async def test_set_with_ttl(self, mock_logger, setup_redis):
        """Test setting a key-value pair with TTL"""
        
        Redis.client = AsyncMock()
        Redis.client.set = AsyncMock()
        
        await Redis.set("test-key", "test-value", ttl=60)
        
        Redis.client.set.assert_called_once_with("test-key", "test-value", ex=60)
        mock_logger.error.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.logger')
    async def test_set_error(self, mock_logger, setup_redis):
        """Test error handling when setting a key fails"""
        
        Redis.client = AsyncMock()
        Redis.client.set = AsyncMock(side_effect=redis.RedisError("Set failed"))
        
        await Redis.set("test-key", "test-value")
        
        Redis.client.set.assert_called_once_with("test-key", "test-value", ex=3600)
        mock_logger.error.assert_called_once_with(
            "Failed to set key test-key in Redis", 
            {"error": "Set failed"}
        )
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.json.loads')
    @patch('ai_agent_utils.services.redis.logger')
    async def test_get_success(self, mock_logger, mock_json_loads, setup_redis):
        """Test successful retrieval of a value"""
        
        Redis.client = AsyncMock()
        mock_value = '{"name": "test"}'
        expected_result = {"name": "test"}
        Redis.client.get = AsyncMock(return_value=mock_value)
        mock_json_loads.return_value = expected_result
        
        result = await Redis.get("test-key")
        
        Redis.client.get.assert_called_once_with("test-key")
        mock_json_loads.assert_called_once_with(mock_value)
        assert result == expected_result
        mock_logger.error.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.logger')
    async def test_get_none_value(self, mock_logger, setup_redis):
        """Test retrieval of a non-existent key"""
        
        Redis.client = AsyncMock()
        Redis.client.get = AsyncMock(return_value=None)
        
        result = await Redis.get("test-key")
        
        Redis.client.get.assert_called_once_with("test-key")
        assert result is None
        mock_logger.error.assert_not_called()
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.json.loads')
    @patch('ai_agent_utils.services.redis.logger')
    async def test_get_json_error(self, mock_logger, mock_json_loads, setup_redis):
        """Test handling of JSON deserialization errors"""
        
        Redis.client = AsyncMock()
        mock_value = '{"name": "test"}'
        Redis.client.get = AsyncMock(return_value=mock_value)
        
        mock_json_loads.side_effect = json.JSONDecodeError("Invalid JSON", "invalid json", 0)
        
        @pytest.mark.asyncio
        @patch('ai_agent_utils.services.redis.redis.Redis')
        async def test_connect_success(self, mock_redis_client, setup_redis):
            """Test successful connection to Redis"""
            mock_instance = AsyncMock()
            mock_redis_client.return_value = mock_instance
            
            await Redis.connect(
                host="test-host", 
                password="test-password",
                username="test-user", 
                port=1234,
                ssl=False,
                similarity_threshold=0.8,
                namespace="test-namespace",
                default_ttl=120
            )
            
            mock_redis_client.assert_called_once_with(
                host="test-host",
                port=1234,
                password="test-password",
                username="test-user",
                socket_keepalive=True,
                ssl=False
            )
            
            assert Redis.similarity_threshold == 0.8
            assert Redis.namespace == "test-namespace"
            assert Redis.default_ttl == 120
            assert Redis.client == mock_instance

        def test_cosine_similarity(self):
            """Test cosine similarity calculation"""
            # Test identical vectors
            vec1 = [1.0, 2.0, 3.0]
            vec2 = [1.0, 2.0, 3.0]
            sim = Redis.cosine_similarity(vec1, vec2)
            assert sim == 1.0
            
            # Test orthogonal vectors
            vec1 = [1.0, 0.0, 0.0]
            vec2 = [0.0, 1.0, 0.0]
            sim = Redis.cosine_similarity(vec1, vec2)
            assert sim == 0.0
            
            # Test with zero vector
            vec1 = [0.0, 0.0, 0.0]
            vec2 = [1.0, 2.0, 3.0]
            sim = Redis.cosine_similarity(vec1, vec2)
            assert sim == 0.0
            
            # Test with a known result
            vec1 = [1.0, 2.0, 3.0]
            vec2 = [4.0, 5.0, 6.0]
            expected = (1*4 + 2*5 + 3*6) / (math.sqrt(1*1 + 2*2 + 3*3) * math.sqrt(4*4 + 5*5 + 6*6))
            assert round(sim, 6) == round(expected, 6)
    
    @pytest.mark.asyncio
    @patch('ai_agent_utils.services.redis.logger')
    async def test_llm_search_with_key_filter_success(self, mock_logger, setup_redis):
        """Test successful search with unique_key_filter"""
        Redis.client = AsyncMock()
        Redis.similarity_threshold = 0.9
        Redis.namespace = "test-ns"
        
        query_embedding = [1.0, 2.0, 3.0]
        unique_key_filter = "test-user-123"
    
        embedding_key = f"embedding:{Redis.namespace}:{unique_key_filter}"
        cached_embedding_str = "1.0,2.0,3.0"  # Perfect match
        Redis.client.get = AsyncMock(side_effect=[
            cached_embedding_str.encode('utf-8'),
            "cached result".encode('utf-8')
        ])

        result = await Redis.llm_search(query_embedding, unique_key_filter)
        
        assert result == "cached result"
        assert Redis.client.get.call_count == 2
        Redis.client.get.assert_any_call(embedding_key)
        Redis.client.get.assert_any_call(f"result:{Redis.namespace}:{unique_key_filter}")
    
        @pytest.mark.asyncio
        @patch('ai_agent_utils.services.redis.logger')
        async def test_llm_search_without_key_filter_success(self, mock_logger, setup_redis):
            """Test successful search without unique_key_filter"""
            Redis.client = AsyncMock()
            Redis.similarity_threshold = 0.9
            Redis.namespace = "test-ns"
            
            query_embedding = [1.0, 2.0, 3.0]
            
            mock_key = f"embedding:{Redis.namespace}:some-key".encode('utf-8')
            
            # Create async iterator for scan_iter
            async def mock_scan_iter_impl(**kwargs):
                yield mock_key
            Redis.client.scan_iter = AsyncMock(return_value=mock_scan_iter_impl())
            
            Redis.client.get = AsyncMock(side_effect=[
                "1.0,2.0,3.0".encode('utf-8'),
                "cached result".encode('utf-8')
            ])
            
            result = await Redis.llm_search(query_embedding)
            
            assert result == "cached result"
            Redis.client.scan_iter.assert_called_once_with(match=f"embedding:{Redis.namespace}:*")
            Redis.client.get.assert_any_call(mock_key)
            Redis.client.get.assert_any_call(f"result:{Redis.namespace}:some-key")

        @pytest.mark.asyncio
        @patch('ai_agent_utils.services.redis.uuid.uuid4')
        @patch('ai_agent_utils.services.redis.asyncio.gather')
        async def test_llm_cache_with_unique_key(self, mock_gather, mock_uuid4, setup_redis):
            """Test caching with provided unique_key"""
            Redis.client = AsyncMock()
            Redis.default_ttl = 3600
            Redis.namespace = "test-ns"
            
            query_embedding = [1.0, 2.0, 3.0]
            result = "test result"
            unique_key = "test-user-123"
            
            await Redis.llm_cache(query_embedding, result, unique_key)
            
            embedding_key = f"embedding:{Redis.namespace}:{unique_key}"
            result_key = f"result:{Redis.namespace}:{unique_key}"
            embedding_str = "1.0,2.0,3.0"
            
            mock_gather.assert_called_once()
            Redis.client.set.assert_any_call(embedding_key, embedding_str, ex=3600)
            Redis.client.set.assert_any_call(result_key, result, ex=3600)
            mock_uuid4.assert_not_called()

        @pytest.mark.asyncio
        @patch('ai_agent_utils.services.redis.uuid.uuid4')
        @patch('ai_agent_utils.services.redis.asyncio.gather')
        async def test_llm_cache_without_unique_key(self, mock_gather, mock_uuid4, setup_redis):
            """Test caching without unique_key (should generate UUID)"""
            Redis.client = AsyncMock()
            Redis.default_ttl = 3600
            Redis.namespace = "test-ns"
            
            query_embedding = [1.0, 2.0, 3.0]
            result = "test result"
            mock_uuid4.return_value = "random-uuid-123"
            
            await Redis.llm_cache(query_embedding, result)
            
            mock_uuid4.assert_called_once()
            embedding_key = f"embedding:{Redis.namespace}:random-uuid-123"
            result_key = f"result:{Redis.namespace}:random-uuid-123"
            
            mock_gather.assert_called_once()
            Redis.client.set.assert_any_call(embedding_key, "1.0,2.0,3.0", ex=3600)
            Redis.client.set.assert_any_call(result_key, result, ex=3600)
            
            Redis.client = AsyncMock()
            Redis.client.delete = AsyncMock()
            
            await Redis.delete("test-key")
            
            Redis.client.delete.assert_called_once_with("test-key")
            mock_logger.error.assert_not_called()

        @pytest.mark.asyncio
        @patch('ai_agent_utils.services.redis.logger')
        async def test_delete_error(self, mock_logger, setup_redis):
            """Test error handling when deleting a key fails"""
            
            Redis.client = AsyncMock()
            Redis.client.delete = AsyncMock(side_effect=redis.RedisError("Delete failed"))
            
            await Redis.delete("test-key")
            
            Redis.client.delete.assert_called_once_with("test-key")
            mock_logger.error.assert_called_once_with(
                "Failed to delete key test-key from Redis", 
                {"error": "Delete failed"}
            )