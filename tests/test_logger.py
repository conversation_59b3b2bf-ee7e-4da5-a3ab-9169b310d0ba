import unittest
from unittest.mock import patch, MagicMock, call
import json
import logging
import sys
import io
from datetime import datetime
from ai_agent_utils.logging.logger import DataDogLogger


class TestDataDogLogger(unittest.TestCase):
    def setUp(self):
        # Reset the logging module before each test
        logging.shutdown()
        logging._handlerList.clear()
        root = logging.getLogger()
        for handler in root.handlers[:]:
            root.removeHandler(handler)
        
        # Create a new DataDogLogger instance for each test
        self.logger = DataDogLogger("test_logger")
        
        # Capture stdout to verify log output
        self.stdout_capture = io.StringIO()
        self.handler = logging.StreamHandler(self.stdout_capture)
        formatter = logging.Formatter("%(message)s")
        self.handler.setFormatter(formatter)
        
        # Remove any existing handlers and add our test handler
        for handler in self.logger.handlers[:]:
            self.logger.removeHandler(handler)
        self.logger.addHandler(self.handler)

    def tearDown(self):
        self.stdout_capture.close()

    def test_init(self):
        """Test that the logger initializes correctly with default settings"""
        logger = DataDogLogger("test_init")
        self.assertEqual(logger.name, "test_init")
        self.assertEqual(logger.level, logging.INFO)
        self.assertTrue(logger.hasHandlers())

    def test_init_with_level(self):
        """Test that the logger initializes with specified level"""
        logger = DataDogLogger("test_level", level=logging.DEBUG)
        self.assertEqual(logger.level, logging.DEBUG)

    def test_format_json_message_basic(self):
        """Test basic formatting of a log message"""
        message = "Test message"
        json_message = self.logger.format_json_message(message)
        log_data = json.loads(json_message)
        self.assertEqual(log_data["message"], message)
        self.assertIn("timestamp", log_data)

    def test_format_json_message_with_facets(self):
        """Test formatting with facets"""
        message = "Test message with facets"
        facets = {"key1": "value1", "key2": 42}
        json_message = self.logger.format_json_message(message, facets)
        log_data = json.loads(json_message)
        self.assertEqual(log_data["message"], message)
        self.assertEqual(log_data["key1"], "value1")
        self.assertEqual(log_data["key2"], 42)
        self.assertIn("timestamp", log_data)

    def test_format_json_message_bytes_conversion(self):
        """Test that bytes are converted to strings in facets"""
        message = "Test bytes conversion"
        facets = {"bytes_data": b"test bytes"}
        json_message = self.logger.format_json_message(message, facets)
        log_data = json.loads(json_message)
        self.assertEqual(log_data["bytes_data"], "test bytes")

    def test_format_json_message_invalid_facets(self):
        """Test that ValueError is raised for invalid facets"""
        with self.assertRaises(ValueError):
            self.logger.format_json_message("Test invalid facets", "not a dict")

    def test_format_json_message_non_serializable_facets(self):
        """Test handling of non-serializable facets"""
        class NonSerializable:
            pass
        
        message = "Test non-serializable facets"
        facets = {"non_serializable": NonSerializable()}
        json_message = self.logger.format_json_message(message, facets)
        log_data = json.loads(json_message)
        self.assertIn("Failed to serialize facets", log_data["message"])

    def test_info_log(self):
        """Test info log method"""
        self.logger.info("Info test")
        log_output = self.stdout_capture.getvalue()
        self.assertIn("[INFO]", log_output)
        self.assertIn("Info test", log_output)

    def test_info_log_with_facets(self):
        """Test info log with facets"""
        self.logger.info("Info test with facets", facets={"test_key": "test_value"})
        log_output = self.stdout_capture.getvalue()
        self.assertIn("[INFO]", log_output)
        self.assertIn("Info test with facets", log_output)
        self.assertIn("test_key", log_output)
        self.assertIn("test_value", log_output)

    def test_error_log(self):
        """Test error log method"""
        self.logger.error("Error test")
        log_output = self.stdout_capture.getvalue()
        self.assertIn("[ERROR]", log_output)
        self.assertIn("Error test", log_output)

    def test_error_log_with_facets(self):
        """Test error log with facets"""
        self.logger.error("Error test with facets", facets={"error_code": 500})
        log_output = self.stdout_capture.getvalue()
        self.assertIn("[ERROR]", log_output)
        self.assertIn("Error test with facets", log_output)
        self.assertIn("error_code", log_output)
        self.assertIn("500", log_output)

    def test_exception_log(self):
        """Test exception log method"""
        try:
            raise ValueError("Test exception")
        except ValueError:
            self.logger.exception("Exception test")
        log_output = self.stdout_capture.getvalue()
        self.assertIn("[EXCEPTION]", log_output)
        self.assertIn("Exception test", log_output)
        self.assertIn("ValueError", log_output)
        self.assertIn("Test exception", log_output)

    def test_critical_log(self):
        """Test critical log method"""
        self.logger.critical("Critical test")
        log_output = self.stdout_capture.getvalue()
        self.assertIn("[CRITICAL]", log_output)
        self.assertIn("Critical test", log_output)

    @patch('ai_agent_utils.logging.logger.statsd')
    @patch('ai_agent_utils.logging.logger.config')
    @patch('ai_agent_utils.logging.logger.os')
    def test_log_metric_gauge(self, mock_os, mock_config, mock_statsd):
        """Test log_metric with gauge type"""
        mock_os.environ.get.return_value = "qa"
        mock_config.service = "test_service"
        
        DataDogLogger.log_metric('gauge', 'test.metric', 42, ['tag1:value1'])
        
        mock_statsd.gauge.assert_called_once_with(
            'test.metric', 
            42, 
            tags=['tag1:value1', 'env:qa', 'service:test_service']
        )

    @patch('ai_agent_utils.logging.logger.statsd')
    @patch('ai_agent_utils.logging.logger.config')
    @patch('ai_agent_utils.logging.logger.os')
    def test_log_metric_increment(self, mock_os, mock_config, mock_statsd):
        """Test log_metric with increment type"""
        mock_os.environ.get.return_value = "qa"
        mock_config.service = "test_service"
        
        DataDogLogger.log_metric('increment', 'test.counter', 1, ['tag1:value1'])
        
        mock_statsd.increment.assert_called_once_with(
            'test.counter', 
            1, 
            tags=['tag1:value1', 'env:qa', 'service:test_service']
        )

    @patch('ai_agent_utils.logging.logger.statsd')
    @patch('ai_agent_utils.logging.logger.config')
    @patch('ai_agent_utils.logging.logger.os')
    def test_log_metric_histogram(self, mock_os, mock_config, mock_statsd):
        """Test log_metric with histogram type"""
        mock_os.environ.get.return_value = "qa"
        mock_config.service = "test_service"
        
        DataDogLogger.log_metric('histogram', 'test.histogram', 100, ['tag1:value1'])
        
        mock_statsd.histogram.assert_called_once_with(
            'test.histogram', 
            100, 
            tags=['tag1:value1', 'env:qa', 'service:test_service']
        )

    def test_handle_and_propagate_error(self):
        """Test handle_and_propagate_error method"""
        exception = ValueError("Test propagated error")
        additional_context = {"context_key": "context_value"}
        
        with self.assertRaises(ValueError) as context:
            self.logger.handle_and_propagate_error(exception, additional_context)
        
        # Verify that the exception is re-raised
        self.assertEqual(str(context.exception), "Test propagated error")
        
        # Verify that the error was logged with the right information
        log_output = self.stdout_capture.getvalue()
        self.assertIn("[ERROR]", log_output)
        self.assertIn("An error occurred", log_output)
        self.assertIn("error_type", log_output)
        self.assertIn("ValueError", log_output)
        self.assertIn("Test propagated error", log_output)
        self.assertIn("context_key", log_output)
        self.assertIn("context_value", log_output)

    def test_handle_and_propagate_error_with_pydantic_model(self):
        """Test handle_and_propagate_error with a Pydantic model as context"""
        from ai_agent_utils.models.ScratchPadSummary import ScratchPadSummary
        from ai_agent_utils.models.ScratchPadUserData import ScratchPadUserData
        from ai_agent_utils.models.ScratchPad import ScratchPad

        exception = ValueError("Test propagated error with Pydantic model")

        scratch_pad = ScratchPad(
            name="Test User",
            partyId=12345,
            memberId=9876,
            entityId="test-entity-id",
            homeClub="TEST1",
            homeClubName="Test Club",
            todaysDate="2023-09-15",
            todaysWeekday="Friday",
            appVersion="1.0.0",
            platform="iOS",
            conversationSummary="User is asking about club hours",
            rag="club hours operation",
            locationNames=["Gym A", "Gym B"],
            associatedMembers=[{"member": "member1"}, {"member": "member2"}],
            nearbyEnabled=True
        )

        additional_context = {
            "scratch_pad": scratch_pad,
            "question": "What are the club hours today?",
            "function": "test_function"
        }

        with self.assertRaises(ValueError) as context:
            self.logger.handle_and_propagate_error(exception, additional_context)

        self.assertEqual(str(context.exception), "Test propagated error with Pydantic model")

        log_output = self.stdout_capture.getvalue()
        self.assertIn("[ERROR]", log_output)
        self.assertIn("An error occurred", log_output)
        self.assertIn("error_type", log_output)
        self.assertIn("ValueError", log_output)

        self.assertIn("Test User", log_output)
        self.assertIn("Test Club", log_output)
        self.assertIn("What are the club hours today?", log_output)
        self.assertIn("test_function", log_output)

        self.stdout_capture.truncate(0)
        self.stdout_capture.seek(0)

        with self.assertRaises(ValueError):
            self.logger.handle_and_propagate_error(exception, scratch_pad)

        log_output = self.stdout_capture.getvalue()
        self.assertIn("Test User", log_output)
        self.assertIn("Test Club", log_output)
