from ddtrace import config
from datadog import statsd
from typing import Any, Dict
from datetime import datetime, timezone
import os
import sys
import json 
import logging

"""
You are meant to override the default logger in your application with the DataDogLogger class.
This class extends the default Python logger and adds additional functionality for structured logging.

Example usage:
# Set the logger class to DataDogLogger
logging.setLoggerClass(DataDogLogger)

# Get the default logger and cast it to DataDogLogger
logger = logging.getLogger(__name__)
logger = DataDogLogger(logger.name)

"""


class DataDogLogger(logging.Logger):
    
    def __init__(self, name: str, level=logging.INFO):
        super().__init__(name, level)

        logging.basicConfig(stream=sys.stdout, level=level)

        # Ensure a console handler is attached by default
        if not self.hasHandlers():
            console_handler = logging.StreamHandler()
            console_handler.setLevel(level)
            formatter = logging.Formatter("%(message)s")  # Simple formatter for JSON output
            console_handler.setFormatter(formatter)
            self.addHandler(console_handler)

    def format_json_message(self, message: str, facets: Dict[str, Any] = None) -> str:
        """
        Format the log message into a JSON string. If JSON conversion fails,
        append problematic facets to the message field as a string.
        """
        if facets is not None and not isinstance(facets, dict):
            print(f"{message}, {facets}")
            raise ValueError("Facets must be a dictionary or None.")
        
        if facets:
            # Convert bytes to strings
            for key, value in facets.items():
                if isinstance(value, bytes):
                    facets[key] = value.decode('utf-8')
        else:
            facets = {}

        # Create a structured log message
        log_message = {
            "message": message,
            "timestamp": datetime.now(timezone.utc).isoformat(),
            **facets,  # Merge facets into the log message
        }

        try:
            # Try to convert the log message to JSON
            return json.dumps(log_message)
        except (TypeError, ValueError) as e:
            # Fallback: Append problematic facets as a string to the message
            fallback_message = {
                "message": f"{message} | Failed to serialize facets: {facets}, error: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
            return json.dumps(fallback_message)

    def info(self, msg: str, *args: Any, **kwargs: Any) -> None:
        if "facets" in kwargs:
            facets = kwargs.pop("facets")
        else:
            facets = None
        json_message = self.format_json_message(msg, facets)
        super().info(f"[INFO]: {json_message}", *args, **kwargs)

    def error(self, msg: str, *args: Any, **kwargs: Any) -> None:
        if "facets" in kwargs:
            facets = kwargs.pop("facets")
        else:
            facets = None
        json_message = self.format_json_message(msg, facets)
        super().error(f"[ERROR]: {json_message}", *args, **kwargs)

    def exception(self, msg: str, *args: Any, exc_info=True, **kwargs: Any) -> None:
        if "facets" in kwargs:
            facets = kwargs.pop("facets")
        else:
            facets = None
        json_message = self.format_json_message(msg, facets)
        super().error(f"[EXCEPTION]: {json_message}", *args, exc_info=exc_info, **kwargs)

    def critical(self, msg: str, *args: Any, **kwargs: Any) -> None:
        if "facets" in kwargs:
            facets = kwargs.pop("facets")
        else:
            facets = None
        json_message = self.format_json_message(msg, facets)
        super().critical(f"[CRITICAL]: {json_message}", *args, **kwargs)

    def log_metric(metric_type, name, value, tags=None):
        if tags is None:
            tags = []
        
        # Add common tags
        tags.append(f'env:{os.environ.get("ENVIRONMENT", "qa")}')
        tags.append(f'service:{config.service}')

        # Log the metric
        if metric_type == 'gauge':
            statsd.gauge(name, value, tags=tags)
        elif metric_type == 'increment':
            statsd.increment(name, value, tags=tags)
        elif metric_type == 'histogram':
            statsd.histogram(name, value, tags=tags)


    def handle_and_propagate_error(self, exception, additional_context=None):
        """
        Logs the exception and propagates it further up the call stack.

        :param logger: The logger instance to use.
        :param exception: The exception to handle and propagate.
        :param additional_context: A dictionary with any additional context to log.
        :raises: The same exception, re-raised after logging.
        """
        facets = {
            "error_type": type(exception).__name__,
            "error_message": str(exception),
        }

        if additional_context:
            # Handle if additional_context is a Pydantic model
            if hasattr(additional_context, "model_dump") and callable(additional_context.model_dump):
                facets.update(additional_context.model_dump())
            # Handle if additional_context is a dict that might contain Pydantic models
            elif isinstance(additional_context, dict):
                processed_context = {}
                for key, value in additional_context.items():
                    # Convert Pydantic models to dictionaries
                    if hasattr(value, "model_dump") and callable(value.model_dump):
                        processed_context[key] = value.model_dump()
                    else:
                        processed_context[key] = value
                facets.update(processed_context)

        self.error("An error occurred.", facets=facets)

        # Re-raise the exception to propagate it up the call stack
        raise exception
