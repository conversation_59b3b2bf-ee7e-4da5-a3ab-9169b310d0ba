
import logging
import os
from ai_agent_utils.logging.logger import DataDogLogger

# Global logger instance
_logger = None

def initialize_logger(service_name: str, level=logging.INFO):
    """Initialize the global DataDogLogger instance"""
    global _logger
    
    # Set the logger class to DataDogLogger
    logging.setLoggerClass(DataDogLogger)
    
    # Create and configure the logger
    _logger = DataDogLogger(service_name, level)
    
    return _logger

# Create a default logger that will be used if initialize_logger is not called
if _logger is None:
    default_service = os.environ.get("SERVICE_NAME", "default_service")
    _logger = initialize_logger(default_service)

# Direct export of the logger instance
logger = _logger