import logging

logger = logging.getLogger(__name__)

'''
    # Example of a function graph node
    # You can have as many conditions as you want
    function1: {
        "next": [
            {"condition": lambda result: result is True, "next_function": function2},
            {"condition": lambda result: result is False, "next_function": function3},
        ]
    }

    # You do not need to have a condition
    # Nodes without conditions should always only have one next node
    function1: {
        "next": [
            {"next_function": function2},
        ]
    }
'''

# FILE: function_graph.py

FUNCTION_GRAPH = {}

def initialize_function_graph(custom_graph):
    if not isinstance(custom_graph, dict):
        raise ValueError("The custom graph must be a dictionary.")
    global FUNCTION_GRAPH
    FUNCTION_GRAPH = custom_graph

def create_node(function_name, next_nodes):
    if function_name in FUNCTION_GRAPH:
        raise KeyError(f"Function '{function_name}' already exists.")
    FUNCTION_GRAPH[function_name] = {"next": next_nodes}

def read_node(function_name):
    return FUNCTION_GRAPH.get(function_name, None)

def update_node(function_name, next_nodes):
    if function_name not in FUNCTION_GRAPH:
        raise KeyError(f"Function '{function_name}' does not exist.")
    FUNCTION_GRAPH[function_name] = {"next": next_nodes}

def delete_node(function_name):
    if function_name not in FUNCTION_GRAPH:
        raise KeyError(f"Function '{function_name}' does not exist.")
    del FUNCTION_GRAPH[function_name]

def list_nodes():
    return list(FUNCTION_GRAPH.items())

def get_function_graph():
    return FUNCTION_GRAPH

def add_condition(function_name, condition, next_function):
    if function_name not in FUNCTION_GRAPH:
        raise KeyError(f"Function '{function_name}' does not exist.")
    FUNCTION_GRAPH[function_name]["next"].append({"condition": condition, "next_function": next_function})

def remove_condition(function_name, condition):
    if function_name not in FUNCTION_GRAPH:
        raise KeyError(f"Function '{function_name}' does not exist.")
    FUNCTION_GRAPH[function_name]["next"] = [node for node in FUNCTION_GRAPH[function_name]["next"] if node.get("condition") != condition]

def update_condition(function_name, old_condition, new_condition, new_next_function):
    if function_name not in FUNCTION_GRAPH:
        raise KeyError(f"Function '{function_name}' does not exist.")
    for node in FUNCTION_GRAPH[function_name]["next"]:
        if node.get("condition") == old_condition:
            node["condition"] = new_condition
            node["next_function"] = new_next_function
            break
    else:
        raise KeyError(f"Condition not found in function '{function_name}'.")

def list_conditions(function_name):
    if function_name not in FUNCTION_GRAPH:
        return []
    return FUNCTION_GRAPH[function_name]["next"]