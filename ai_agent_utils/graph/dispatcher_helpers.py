import inspect
import asyncio
from typing import Callable, List, Dict, Any
from ai_agent_utils.graph.dispatcher_function_graph import get_function_graph

# Executes the given function with the provided arguments.
async def execute_function(func: Callable, *args, **kwargs) -> Any:
    # Check if the func is callable (and resolve if it's a string or some other reference)
    if not callable(func):
        raise TypeError(f"{func} is not a callable object")

    # If it's a coroutine, use the appropriate await syntax
    if inspect.iscoroutinefunction(func):
        sig = inspect.signature(func)
        func_kwargs = {k: v for k, v in kwargs.items() if k in sig.parameters}
        return await func(*args, **func_kwargs)
    else:
        return func(*args, **kwargs)

# Executes multiple functions concurrently and gathers results
async def execute_concurrent_functions(functions: List[Callable], input_data: Dict[str, Any]) -> List[Any]:
    tasks = [execute_function(func, **input_data) for func in functions]
    return await asyncio.gather(*tasks)

def get_next_function(current_function: Callable, result: Any) -> Callable:
    function_graph = get_function_graph()
    node = function_graph.get(current_function.__name__)

    if node:
        # If there are dependencies, ensure they're resolved before continuing
        if node.get("dependencies"):
            # Check if all dependencies have been resolved (parallel tasks should be complete)
            unresolved_dependencies = [
                dep for dep in node.get("dependencies") if dep not in function_graph
            ]
            if unresolved_dependencies:
                return None  # If dependencies are unresolved, don't proceed

        # Look through conditions and choose the next function based on the result
        for next_node in node.next:
            condition = next_node.get("condition")
            next_function = next_node.get("next_function")

            if condition is None or condition(result):
                return next_function
    return None