import asyncio
import inspect
import logging
from typing import Callable, Dict, Any, List
from ai_agent_utils.graph.dispatcher_function_graph import get_function_graph
from ai_agent_utils.graph.dispatcher_helpers import execute_function, execute_concurrent_functions, get_next_function
from ai_agent_utils.logging.logger import DataDogLogger

# Get the default logger and cast it to DataDogLogger
logger = logging.getLogger(__name__)
logger = DataDogLogger(logger.name)

async def evaluate_condition(condition: Callable, res: Any) -> bool:
    """
    Evaluates a condition function, running it in an executor if it is synchronous.
    """
    if inspect.iscoroutinefunction(condition):
        return await condition(res)
    else:
        loop = asyncio.get_running_loop()
        return await loop.run_in_executor(None, condition, res)


async def get_next_function_parallel(current_func: Callable, res: Any, next_nodes: List[Dict[str, Any]]) -> Callable:
    """
    Evaluates all the conditions for the next nodes concurrently and returns the first next_function
    whose condition returns True (in the original node order). If none are True, falls back to get_next_function.
    """
    tasks = []
    for node in next_nodes:
        condition = node.get("condition")
        if condition:
            tasks.append(evaluate_condition(condition, res))
        else:
            # No condition means False by default.
            tasks.append(asyncio.sleep(0, result=False))
    cond_results = await asyncio.gather(*tasks)
    for node, cond in zip(next_nodes, cond_results):
        if cond:
            return node.get("next_function")
    return get_next_function(current_func, res)


async def run_current_function(
    func: Callable,
    function_results: Dict[str, Any],
    args,
    kwargs,
    function_graph: Dict[str, Any]
):
    """
    Executes a function with merged keyword arguments (from both kwargs and shared state) and updates the shared state.
    Returns the result and its corresponding graph node.
    """
    combined_kwargs = {**kwargs, **function_results}
    node = function_graph.get(func.__name__)

    if node and node.get("bubble_up"):
      res = execute_function(func, *args, **combined_kwargs)
      return res, node

    res = await execute_function(func, *args, **combined_kwargs)
    update_function_results(function_results, res)
    return res, node


async def process_parallel_branch(
    current_func: Callable,
    res: Any,
    node: Dict[str, Any],
    function_results: Dict[str, Any],
    function_graph: Dict[str, Any],
    args,
    kwargs
):
    """
    Processes a branch that contains parallel tasks. It concurrently executes all functions defined in the
    'parallel' branch, updates the shared state with their results, and then processes any subsequent 'next' nodes.
    """
    parallel_funcs = [pn["next_function"] for pn in node.get("parallel", [])]
    parallel_results = await execute_concurrent_functions(parallel_funcs, function_results)
    for pr in parallel_results:
        update_function_results(function_results, pr)

    if node.get("next"):
        next_nodes = node.get("next")
        for _ in next_nodes:
            nf = get_next_function(current_func, res)
            if nf:
                current_func = nf
            res, node = await run_current_function(current_func, function_results, args, kwargs, function_graph)
            nf = get_next_function(current_func, res)
            if nf:
                current_func = nf
        return current_func
    return None


async def process_non_parallel_branch(
    current_func: Callable,
    res: Any,
    node: Dict[str, Any],
    function_results: Dict[str, Any]
) -> Callable:
    """
    Processes a branch that does not contain parallel tasks. It first checks if the node is marked as 'result only'
    (in which case the shared state is updated and the same function is continued) or, if not, concurrently evaluates
    all conditions in the 'next' nodes.
    """
    if node and node.get("bubble_up"):
        update_function_results(function_results, res)
        return current_func

    if node and node.get("next"):
        next_nodes = node.get("next")
        next_fn = await get_next_function_parallel(current_func, res, next_nodes)
        return next_fn

    return None


def update_function_results(results: Dict[str, Any], result: Any):
    """
    Updates the shared state with the function result.
    """
    if isinstance(result, str):
        results["answer"] = result
    elif isinstance(result, dict):
        results.update(result)
    else:
        # Fallback: store the result keyed by its __name__
        results[result.__name__] = result


async def handle_function_graph(
    starting_function: Callable,
    scratch_pad: dict,
    history: list,
    question: str,
    stream: bool,
    *args,
    **kwargs
):
    """
    Traverses and executes the function graph beginning from the starting_function.
    Depending on the node configuration, it may run functions in parallel or sequentially.
    """
    results = {"answer": "An error occurred.", "path": "No Intent"}
    function_graph = get_function_graph()

    if starting_function.__name__ not in function_graph:
        logger.error(f"Function '{starting_function}' not found.", facets={"function": starting_function.__name__})
        return results

    # Shared state passed between function calls.
    function_results = {"scratch_pad": scratch_pad, "history": history, "question": question, "stream": stream}
    current_function = starting_function

    try:
        while current_function:
            res, node = await run_current_function(current_function, function_results, args, kwargs, function_graph)

            if node and node.get("bubble_up"):
              return await res
            if node and node.get("parallel"):
                current_function = await process_parallel_branch(
                    current_function, res, node, function_results, function_graph, args, kwargs
                )
                if not current_function:
                    # If no further function is defined after parallel tasks, return the result.
                    results = res if isinstance(res, dict) else {"answer": res}
                    break
                continue

            next_function = await process_non_parallel_branch(current_function, res, node, function_results)
            if next_function:
                current_function = next_function
            else:
                results = res if isinstance(res, dict) else {"answer": res}
                break

    except Exception as e:
        logger.handle_and_propagate_error(
            e,
            additional_context={"scratch_pad": scratch_pad, "question": question, "function": current_function.__name__}
        )
        return {"answer": "An error occurred while processing your request.", "path": current_function.__name__}

    return results


async def function_dispatch(
    question: str = "",
    scratch_pad: dict = {},
    history: list = [],
    starting_function: Callable = None,
    stream: bool = False
):
    """
    Dispatches the function graph processing starting from the provided starting_function.
    """
    return await handle_function_graph(starting_function, scratch_pad, history, question, stream)