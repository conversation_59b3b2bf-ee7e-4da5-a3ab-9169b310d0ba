from typing import Optional
from pydantic import BaseModel, Field

class ScratchPadSummary(BaseModel):
  conversationSummary: Optional[str] = Field(
    description="Summarize the conversation history and the question asked in a way that makes it clear to another agent what the user is asking."
  )
  rag: Optional[str] = Field(
    description="Required field - RAG text keywords to easily retrieve documents for the users question"
  )
  locationNames: Optional[list[str]] = Field(
    description="List of location names in the relevant question, defaults to system information homeClubName if none referenced in history"
  )
  nearbyEnabled: Optional[bool] = Field(
    description="True or false, indicates whether the user is asking about location or near me queries"
  )

  def get(self, attr, default=None):
    return getattr(self, attr, default)
