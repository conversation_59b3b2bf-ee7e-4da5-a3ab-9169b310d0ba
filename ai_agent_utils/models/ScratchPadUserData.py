from pydantic import BaseModel
from typing import Optional

class ScratchPadUserData(BaseModel):
    name: str
    partyId: int
    memberId: int
    entityId: str
    homeClub: str
    homeClubName: str
    todaysDate: str
    todaysWeekday: str
    appVersion: str
    platform: str
    roles: Optional[list[str]] = None
    associatedMembers: Optional[list[dict]] = None
    conversationId: Optional[str] = None
    longitude: Optional[float] = None
    latitude: Optional[float] = None