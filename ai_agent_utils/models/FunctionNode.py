from pydantic import BaseModel
from typing import List, Dict, Callable

class FunctionNode(BaseModel):
    bubble_up: bool = False
    dependencies: List[str] = []
    parallel: List[Dict[str, Callable]] = []
    next: List[Dict[str, Callable]] = []

    def get(self, field, default=None):
      if hasattr(self, field):
          return getattr(self, field)
      else:
          return default # Or raise an exception, or return a default value