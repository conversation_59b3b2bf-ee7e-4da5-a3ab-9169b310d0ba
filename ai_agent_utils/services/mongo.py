import os
from dotenv import load_dotenv
import pymongo

load_dotenv()

class MongoDBClient:
    client: pymongo.MongoClient = None

    @classmethod
    def connect(cls, connection_string: str):
        connection_string = connection_string or os.getenv("MONGO_CONNECTION_STRING")
        try:
            cls.client = pymongo.MongoClient(connection_string)
        except pymongo.errors.ConnectionError as e:
            print(f"Failed to connect to MongoDB: {e}")
            raise

    @classmethod
    def close(cls):
        if cls.client is not None:
            cls.client.close()

    @classmethod
    def vector_search(cls, embeddings: list, namespace: str, pre_filter: dict = None, post_filter: list = None, k: int = 10, include_score: bool = False, projection: dict = None):
        if cls.client is None:
            raise Exception("MongoDB client is not connected")

        score = 0
        if include_score:
            score = {'$meta': 'vectorSearchScore'}

        if not pre_filter:
            pre_filter = {}

        if not post_filter:
            post_filter = []

        default_projection = {
            '_id': 0,
            'embedding': 0,
            'chunkAlgoHash': 0,
            'chunkIndex': 0,
            'sourceName': 0,
            'tokenCount': 0,
            'updated': 0,
            'score': score
        }

        if projection:
            default_projection.update(projection)

        pipeline = [
            {
              '$vectorSearch': {
                  'index': 'vector_index',
                  'path': 'embedding',
                  'filter': pre_filter,
                  'queryVector': embeddings,
                  'numCandidates': 150,
                  'limit': k
              }
            },
            *post_filter,
            {
                '$project': default_projection
            }
        ]

        db_name, collection_name = namespace.split(".")
        results = cls.client[db_name][collection_name].aggregate(pipeline)

        blacklist = ['metadata', 'score', 'reference']
        full_results = []
        filtered_results = []
        for i in results:
            new_d = {k: i[k] for k in set(list(i.keys())) - set(blacklist)}
            filtered_results.append(new_d)
            full_results.append(i)

        return {
            'page_content': filtered_results.__str__(),
            'results': full_results
        }

    @classmethod
    def find_one(cls, namespace: str, query: dict, **kwargs):
        db_name, collection_name = namespace.split(".")
        return cls.client[db_name][collection_name].find_one(query, **kwargs)

    @classmethod
    def find_many(cls, namespace: str, query: dict, **kwargs):
        db_name, collection_name = namespace.split(".")
        return cls.client[db_name][collection_name].find(query, **kwargs)
 