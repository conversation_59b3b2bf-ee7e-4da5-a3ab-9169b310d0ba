import dotenv
import os
import logging
from openai import AsyncAzureOpenAI, NOT_GIVEN
from cachetools import TTLCache


dotenv.load_dotenv()

class OpenAIClient:
    client: AsyncAzureOpenAI = None
    circuit_client: AsyncAzureOpenAI = None
    model: str = None
    circuit_model: str = None
    cache = TTLCache(maxsize=1, ttl=60)
    logger = logging.getLogger(__name__)

    @classmethod
    async def connect(cls, api_key=None, azure_endpoint=None, api_version=None, model=None):
        api_key = api_key or os.getenv("AZURE_OPENAI_PTU_API_KEY")
        azure_endpoint = azure_endpoint or os.getenv("AZURE_OPENAI_PTU_ENDPOINT")
        api_version = api_version or os.getenv("OPENAI_API_VERSION")
        cls.model = model or os.getenv("CHAT_PTU_DEPLOYMENT_NAME")

        try:
            cls.client = AsyncAzureOpenAI(
                api_key=api_key,
                azure_endpoint=azure_endpoint,
                api_version=api_version
            )
        except Exception as e:
            cls.logger.error("Failed to connect to Azure OpenAI", {"exception": str(e)})
            raise
        
    @classmethod
    async def connect_circuit(cls, api_key=None, azure_endpoint=None, api_version=None, model=None):
        api_key = api_key or os.getenv("AZURE_OPENAI_API_KEY")
        azure_endpoint = azure_endpoint or os.getenv("AZURE_OPENAI_ENDPOINT")
        api_version = api_version or os.getenv("OPENAI_API_VERSION")
        cls.circuit_model = model or os.getenv("CHAT_DEPLOYMENT_NAME")

        try:
            cls.circuit_client = AsyncAzureOpenAI(
                api_key=api_key,
                azure_endpoint=azure_endpoint,
                api_version=api_version
            )
        except Exception as e:
            cls.logger.error("Failed to connect to Azure OpenAI", {"exception": str(e)})
            raise

    @classmethod
    async def close(cls):
        if cls.client is not None:
            await cls.client.close()

    @classmethod
    async def circuit_close(cls):
        if cls.circuit_client is not None:
            await cls.circuit_client.close()

    @classmethod
    async def get_chat(cls):
        return cls.client

    @classmethod
    async def generate_embedding(cls, content: str):
        try:
            response = await cls.client.embeddings.create(
                input=content,
                model=os.getenv("EMBEDDING_DEPLOYMENT_NAME")
            )
            return response.data[0].embedding
        except Exception as e:
            cls.logger.error("Failed to generate embedding", {"exception": str(e)})
            return None

    @classmethod
    async def chat_create(cls, messages: list, tools: list = NOT_GIVEN, tool_choice: str = NOT_GIVEN, response_format: dict = NOT_GIVEN, parallel_tool_calls: bool = NOT_GIVEN, temperature: float = 0.4, top_p: float = NOT_GIVEN, stream=False, seed: int = NOT_GIVEN):
        try:
            params = {
                "messages": messages,
                "tools": tools,
                "tool_choice": tool_choice,
                "parallel_tool_calls": parallel_tool_calls,
                "stream": stream,
                "temperature": temperature,
                "top_p": top_p,
                "seed": seed,
                "response_format": response_format
            }

            if "OPEN_CIRCUIT" in cls.cache:
                response = await cls.circuit_client.chat.completions.create(
                    model=cls.circuit_model,
                    **params
                )
            else:
                response = await cls.client.chat.completions.create(
                    model=cls.model,
                    **params
                )
            return response
        except Exception as e:
            cls.logger.error("Failed to create chat", {"exception": str(e)})
            if "OPEN_CIRCUIT" not in cls.cache:
                cls.open_circuit()
            response = await cls.circuit_client.chat.completions.create(
                model=cls.circuit_model,
                **params
            )
            return response

    @classmethod
    async def chat_structured(cls, messages: list, tools: list = NOT_GIVEN, response_format=None, parallel_tool_calls: bool = NOT_GIVEN, temperature: float = 0.4, top_p: float = NOT_GIVEN, seed: int = NOT_GIVEN):
        try:
            params = {
                "temperature": temperature,
                "top_p": top_p,
                "seed": seed,
                "messages": messages,
                "tools": tools,
                "parallel_tool_calls": parallel_tool_calls,
                "response_format": response_format
            }

            if "OPEN_CIRCUIT" in cls.cache:
                response = await cls.circuit_client.beta.chat.completions.parse(
                    model=cls.circuit_model,
                    **params
                )
            else:
                response = await cls.client.beta.chat.completions.parse(
                    model=cls.model,
                    **params
                )
            return response.choices[0].message.parsed
        except Exception as e:
            cls.logger.error("Failed to create structured chat", {"exception": str(e)})
            if "OPEN_CIRCUIT" not in cls.cache:
                await cls.open_circuit()
            response = await cls.circuit_client.beta.chat.completions.parse(
                model=cls.circuit_model,
                **params
            )
            return response.choices[0].message.parsed
        
    @classmethod
    async def open_circuit(cls):
        cls.logger.info("Open circuit")
        # Check if client is connected
        if cls.circuit_client is None:
            await cls.connect_circuit()
        cls.cache["OPEN_CIRCUIT"] = True