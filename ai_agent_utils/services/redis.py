import redis.asyncio as redis
import json
import uuid
import math
import asyncio
from typing import Optional, List
import logging
from ai_agent_utils.logging.logger import DataDogLogger

# Get the default logger and cast it to DataDogLogger
logger = logging.getLogger(__name__)
logger = DataDogLogger(logger.name)

class Redis:
  client: redis.Redis = None

  @staticmethod
  def cosine_similarity(vec1: List[float], vec2: List[float]) -> float:
      """
      Compute cosine similarity between two vectors purely in Python
      """
      dot_product = sum(x * y for x, y in zip(vec1, vec2))
      norm1 = math.sqrt(sum(x * x for x in vec1))
      norm2 = math.sqrt(sum(x * x for x in vec2))
      if norm1 == 0 or norm2 == 0:
          return 0.0
      return dot_product / (norm1 * norm2)
      
  @classmethod
  async def connect(
    cls,
    host: str,
    password: str,
    username: str = None,
    port: int = 6380,
    ssl: bool = True,
    similarity_threshold: float = 0.9,
    namespace: str = 'default',
    default_ttl: int = 3600  # Default TTL for cached results 1 Hour
  ):
    """
    Connect to a Redis server and configure caching settings.
    This asynchronous class method establishes a connection to a Redis server
    and sets up default values for caching operations (similarity threshold,
    namespace, and time-to-live).
    Parameters:
      host (str): The hostname or IP address of the Redis server.
      password (str): Authentication password for the Redis server.
      username (str, optional): Username for Redis servers that use ACL authentication. Defaults to None.
      port (int, optional): The port number of the Redis server. Defaults to 6380.
      ssl (bool, optional): Whether to use SSL for the connection. Defaults to True.
      similarity_threshold (float, optional): Threshold value for similarity matching in caching. 
                           Defaults to 0.9.
      namespace (str, optional): Namespace prefix for Redis keys to avoid collisions. Defaults to None.
      default_ttl (int, optional): Default time-to-live in seconds for cached items. Defaults to 3600 (1 hour).
    Raises:
      redis.RedisError: If connection to Redis server fails.
    Returns:
      None: This method doesn't return any value but sets up the Redis client for the class. 
    """
    cls.similarity_threshold = similarity_threshold
    cls.namespace = namespace
    cls.default_ttl = default_ttl
    
    try:
    # Connect to Redis server
        cls.client = redis.Redis(
          host=host,
          port=port,
          password=password,
          username=username,
          socket_keepalive=True,
          ssl=ssl
        )
    except redis.RedisError as e:
        logger.error("Failed to connect to Redis", {"error": str(e)})
        raise

    await cls.client
  
  @classmethod
  async def close(cls):
    if cls.client is not None:
        await cls.client.aclose()

  @classmethod
  async def set(cls, key: str, value: str, ttl: Optional[int] = None):
    try: 
      effective_ttl = ttl if ttl is not None else cls.default_ttl
      await cls.client.set(key, value, ex=effective_ttl)
    except redis.RedisError as e:
      logger.error(f"Failed to set key {key} in Redis", {"error": str(e)})

  @classmethod
  async def get(cls, key: str):
    try:
      value = await cls.client.get(key)
      if value:
        try:
          return json.loads(value)
        except json.JSONDecodeError:
          # If not valid JSON, decode as string
          return value.decode('utf-8')

      return value
    except redis.RedisError as e:
      logger.error(f"Failed to get key {key} from Redis", {"error": str(e)})
      return None
  
  @classmethod
  async def delete(cls, key: str):
    try:
      await cls.client.delete(key)
    except redis.RedisError as e:
      logger.error(f"Failed to delete key {key} from Redis", {"error": str(e)})
    
  @classmethod
  async def llm_search(cls, query_embedding: List[float], unique_key_filter: Optional[str] = None) -> Optional[str]:
      """
      Search for a cached result based on query_embedding.
      If a unique_key_filter is provided, only the cache entry with that key is checked.
      
      The cache key format is:
        - If unique_key_filter is provided: "embedding:{namespace}:{unique_key_filter}"
        - Otherwise: all keys matching "embedding:{namespace}:*"
      """
      if unique_key_filter:
          key = f"embedding:{cls.namespace}:{unique_key_filter}"
          cached_embedding_str = await cls.client.get(key)
          if cached_embedding_str:
              cached_embedding = [float(x) for x in cached_embedding_str.decode("utf-8").split(",")]
              sim = cls.cosine_similarity(query_embedding, cached_embedding)
              if sim > cls.similarity_threshold:
                  result_key = f"result:{cls.namespace}:{unique_key_filter}"
                  cached_result = await cls.client.get(result_key)
                  if cached_result:
                      return cached_result.decode("utf-8")
          return None
      else:
          pattern = f"embedding:{cls.namespace}:*"
          best_score = -1.0
          best_key = None
          async for key in cls.client.scan_iter(match=pattern):
              cached_embedding_str = await cls.client.get(key)
              if not cached_embedding_str:
                  continue
              cached_embedding = [float(x) for x in cached_embedding_str.decode("utf-8").split(",")]
              sim = cls.cosine_similarity(query_embedding, cached_embedding)
              if sim > best_score:
                  best_score = sim
                  best_key = key.decode("utf-8")
          if best_score > cls.similarity_threshold and best_key:
              result_key = best_key.replace("embedding", "result", 1)
              cached_result = await cls.client.get(result_key)
              if cached_result:
                  return cached_result.decode("utf-8")
          return None
        
  @classmethod
  async def llm_cache(cls, query_embedding: List[float], result: str, unique_key: Optional[str] = None, ttl: Optional[int] = None) -> None:
    """
    Cache the result along with its embedding.
    
    If a unique_key is provided (e.g. a user's partyId / entityId), it is used as the unique identifier in the cache key.
    Otherwise, a random UUID is generated.
    
    Cache keys will be:
      - With unique_key: "embedding:{namespace}:{unique_key}" and "result:{namespace}:{unique_key}"
      - Without unique_key: "embedding:{namespace}:{random_uuid}" and "result:{namespace}:{random_uuid}"
    """
    key_id = unique_key if unique_key else str(uuid.uuid4())
    embedding_key = f"embedding:{cls.namespace}:{key_id}"
    result_key = f"result:{cls.namespace}:{key_id}"
    embedding_str = ",".join(map(str, query_embedding))
    effective_ttl = ttl if ttl is not None else cls.default_ttl
    await asyncio.gather(
      cls.client.set(embedding_key, embedding_str, ex=effective_ttl),
      cls.client.set(result_key, result, ex=effective_ttl)
    )
