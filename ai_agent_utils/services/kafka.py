import os
import logging
import platform
import json
import pytz
from datetime import datetime
from ai_agent_utils.logging.logger import <PERSON>Dog<PERSON>ogger
from confluent_kafka import Producer

class KafkaClient:
    logger = DataDogLogger(logging.getLogger(__name__).name)
    conf: dict = None
    producer: Producer = None

    @classmethod
    def __init__(cls, client_id=None, brokers=None, username=None, password=None):
        cls.kafka_conf(client_id, brokers, username, password)
    
    @classmethod
    def delivery_report(cls, err, msg):
        """
        Reports the success or failure of a message delivery.

        Args:
            err (KafkaError): The error that occurred on None on success.
            msg (Message): The message that was produced or failed.
        """
        if err is not None:
            cls.logger.error("Delivery failed for User record", {"key": msg.key(),"topic": msg.topic(),"error": str(err)})
            return

    @classmethod
    def kafka_conf(cls, client_id=None, brokers=None, username=None, password=None):
        cls.conf = {
            'bootstrap.servers': brokers or os.environ.get("KAFKA_BROKERS"),
            'client.id': client_id or os.environ.get("KAFKA_CLIENT_ID"),
            'sasl.username': username or os.environ.get("KAFKA_SASL_USERNAME"),
            'sasl.password': password or os.environ.get("KAFKA_SASL_PASSWORD"),
            'sasl.mechanism': 'PLAIN',
            'security.protocol': 'SASL_SSL',
            'statistics.interval.ms': 0,  # Disable stats collection
            'enable.metrics.push': False # disable weird telemetry logging
        }
        cls.producer = Producer(cls.conf)

    @classmethod
    def _handle_timing_output(cls, func, service_name, model, metadata, execution_time, debug):
        """Shared logic for handling timing output and Kafka production."""
        output = {
            "Service": f'{service_name}',
            "Function": f'{func.__name__}',
            "OS_Version": f'{platform.system()}',
            "Model": f'{model}',
            "Execution_Time": f'{execution_time:.12f}',
            "Metadata": f'{metadata}',
            "PublishTimestamp": datetime.now(pytz.UTC).strftime('%Y-%m-%dT%H:%M:%S.%f')[:-3] + 'Z'
        }
        
        out_str = json.dumps(output)
        
        if debug:
            print(f"[DEBUG] Performance Metrics: {out_str}")
        
        cls.producer.produce(
            topic=os.environ["AI_LAIC_CHAT_PERFORMANCE_TOPIC"],
            key=json.dumps({"service": service_name}),
            value=out_str,
            on_delivery=KafkaClient.delivery_report
        )
        cls.producer.flush()