import os
from ldclient.client import LDClient
from ldclient.config import Config
from ldclient.context import Context
from cachetools import TTLCache
from ai_agent_utils.logging.logger import DataDogLogger


class LaunchDarklyClient:
    client: LDClient = None
    cache = TTLCache(maxsize=1, ttl=60)
    logger = DataDogLogger(__name__)

    @classmethod
    def connect(cls, sdk_key=None):
        sdk_key = sdk_key or os.getenv("LD_SDK_KEY")
        if not sdk_key:
            raise Exception("SDK Key is required")

        try:
            cls.client = LDClient(config=Config(sdk_key=sdk_key))
        except Exception as e:
            cls.logger.error("Failed to connect to LaunchDarkly", {
                             "exception": str(e)})
            raise

    @classmethod
    def close(cls):
        if cls.client is not None:
            cls.client.close()

    @classmethod
    def get_user_context(cls, scratch_pad):
        entityId = scratch_pad.get("entityId", "default")
        context_builder = Context.builder(entityId)
        context_builder.set("partyId", scratch_pad.get("partyId", ""))
        context_builder.set("memberId", scratch_pad.get("memberId", ""))
        context_builder.set("appVersion", scratch_pad.get("appVersion", ""))
        context_builder.set("platform", scratch_pad.get("platform", ""))
        return context_builder.build()
