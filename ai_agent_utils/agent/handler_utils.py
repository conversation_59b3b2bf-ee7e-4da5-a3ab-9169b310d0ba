import logging
import httpx
import traceback
from typing import AsyncGenerator, Dict, Any, List, Optional
from ai_agent_utils.logging.logger import DataDogLogger


async def make_api_request(
    url: str,
    question: str, 
    history: List[Dict[str, Any]], 
    scratch_pad: Dict[str, Any],
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 4000,
    path: Optional[str] = "Default",
    answer: Optional[str] = "I am sorry, I am not able to process your request at the moment. Please try again later."
) -> Dict[str, Any]:
    """Make a regular API request to the chat endpoint."""
    async with httpx.AsyncClient() as client:
        logger = DataDogLogger(logging.getLogger(__name__).name)
        try:
            response = await client.post(
                url=url,
                json={"question": question, "history": history, "scratch_pad": scratch_pad},
                headers=headers,
                timeout=timeout
            )
            response.raise_for_status()
        except Exception:
            logger.error(traceback.format_exc())
            return {
                "answer": answer,
                "path": path
                }

        result = response.json()

        if not isinstance(result, dict):
            raise ValueError("The API response must be a dictionary.")

        return result


async def create_event_generator(
    url: str,
    question: str, 
    history: List[Dict[str, Any]], 
    scratch_pad: Dict[str, Any],
    headers: Optional[Dict[str, str]] = None,
    timeout: int = 40000
) -> AsyncGenerator[str, None]:
    """Create an event generator for streaming responses."""
    async with httpx.AsyncClient(timeout=timeout) as client:
        if headers:
            headers["Accept"] = "text/event-stream"
        else:
            headers = {"Accept": "text/event-stream"}
        async with client.stream(
            "POST",
            url,
            json={"question": question, "history": history, "scratch_pad": scratch_pad},
            headers=headers,
        ) as response:
            response.raise_for_status()
            async for chunk in response.aiter_text():
                yield chunk

async def create_stream_object(value, obj_type="content"):
    """
    Create an object with the structure expected by stream_response
    This mocks the structure expected by stream_response and what is normally produced by the OpenAI Async Generator
    This can be used when wrapping the OpenAI Async Generator in another generator to control the output
    """
    # Initialize with all required attributes
    class MockDelta:
        def __init__(self):
            # Default all attributes to None
            self.content = None
            self.suggestions = None
            self.path = None
            self.references = None
            self.metadata = None
            self.stream_end = None
            
    class MockChoice:
        def __init__(self, delta):
            self.delta = delta
            
    class MockResponse:
        def __init__(self, choices):
            self.choices = choices
    
    # Create delta with all attributes initialized
    delta = MockDelta()
    
    # Set the specific attribute based on type
    if obj_type == "content":
        delta.content = str(value) if value is not None else ""
    elif obj_type == "suggestions":
        delta.suggestions = value
        delta.content = ""
    elif obj_type == "path":
        delta.path = value
        delta.content = ""
    elif obj_type == "references":
        delta.references = value
        delta.content = ""
    elif obj_type == "metadata":
        delta.metadata = value
        delta.content = ""
    elif obj_type == "stream_end":
        # Special case for stream end marker
        delta.stream_end = True
        delta.content = ""
    else:
        delta.content = str(value) if value is not None else ""
        
    choice = MockChoice(delta)
    return MockResponse([choice])
