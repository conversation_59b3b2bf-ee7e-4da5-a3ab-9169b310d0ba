import asyncio
import os
from functools import wraps
import time
from ai_agent_utils.services.kafka import KafkaClient

from confluent_kafka import Producer
from ai_agent_utils.services.kafka import KafkaClient


def collect_metadata(*args, **kwargs):
    metadata=None
    _scratch_pad = kwargs.get('scratch_pad')
    if _scratch_pad:
        metadata =  {"partyId": _scratch_pad.get('partyId')}
        metadata["conversationId"] = _scratch_pad.get('conversationId')
    
    return metadata


def track_time(service_name, model):
    """Smart decorator that handles both async and sync functions."""
    def decorator(func):
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def wrapper(*args, **kwargs):
                metadata = collect_metadata(*args, **kwargs)
                start_time = time.time()
                result = await func(*args, **kwargs)
                execution_time = time.time() - start_time
                debug = os.getenv("DEBUG", "False").lower() == "true"
                KafkaClient._handle_timing_output(func, service_name, model, metadata, execution_time, debug)
                return result
            return wrapper
        else:
            @wraps(func)
            def wrapper(*args, **kwargs):
                metadata = collect_metadata(*args, **kwargs)
                start_time = time.time()
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                debug = os.getenv("DEBUG", "False").lower() == "true"
                KafkaClient._handle_timing_output(func, service_name, model, metadata, execution_time, debug)
                return result
            return wrapper
    return decorator