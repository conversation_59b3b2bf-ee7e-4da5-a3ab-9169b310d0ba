from ai_agent_utils.models.Document import Document

def simplify_history(history: list[dict], include_system: bool = False, include_assistant: bool = True, include_user: bool = True) -> str:
    historic_content = ""
    for item in history:
        if item["role"] == "system" and include_system:
            historic_content += f"# system: {item['content']}\n"
        if item["role"] == "user" and include_user:
            historic_content += f"# user: {item['content']}\n"
        if item["role"] == "assistant" and include_assistant:
            historic_content += f"# assistant: {item['content']}\n"

    return historic_content

# This function takes a list of documents and returns a list of unique reference links.
def make_reference_links(documents: list[Document], default: list = None, reference: str="metadata"):

    links = []
    for document in documents:
        if isinstance(document, tuple):
            document = document[0]

        if documents:
            link="" # make sure we dont blow up
            if isinstance(document, dict) and reference in document:
                link = document[reference]["link"]       
            if any(
                x["link"].lower() == link.lower() for x in links
            ): 
                continue
            
        links.append(document[reference])
        
    if not links and default:
        return default

    return links