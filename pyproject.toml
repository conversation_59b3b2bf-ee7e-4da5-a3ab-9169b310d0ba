[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "ai-agent-utils"
version = "0.1.26"
description = "A shared library for AI agent utilities"
authors = [{ name = "<PERSON>", email = "<EMAIL>" }]
license = { text = "MIT" }
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "cachetools>=5.5.2",
    "confluent-kafka>=2.10.0",
    "datadog>=0.51.0",
    "ddtrace>=3.7.0",
    "jinja2>=3.1.6",
    "launchdarkly-server-sdk>=9.11.0",
    "openai>=1.78.1",
    "pymongo>=4.13.0",
    "pytest-asyncio>=0.26.0",
    "python-dotenv>=1.1.0",
    "pytz>=2025.2",
    "redis>=6.1.0",
    "requests>=2.32.3",
    "ruff>=0.11.10",
    "websockets>=15.0.1",
]
classifiers = [
  "Programming Language :: Python :: 3",
  "License :: OSI Approved :: MIT License",
  "Operating System :: OS Independent"
]

[project.optional-dependencies]
dev = [
    "black>=25.1.0",
    "flake8>=7.2.0",
    "isort>=6.0.1",
    "pytest>=8.3.5",
]

[tool.setuptools]
packages = { find = { include = ["ai_agent_utils*"] } }

[tool.setuptools.package-data]
ai_agent_utils = ["*"]
